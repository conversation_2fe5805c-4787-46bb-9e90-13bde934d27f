package kr.wayplus.qr_hallimpark.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;

/**
 * 홈 페이지 컨트롤러
 * - 사용자 권한에 따라 다른 페이지 제공
 */
@Controller
public class HomeController {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * 메인 홈 페이지 - 사용자 권한에 따라 다른 내용 표시
     */
    @GetMapping("/")
    public String home(Model model, HttpServletRequest request) {
        logger.debug("Home page requested");

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        boolean isAuthenticated = auth != null && auth.isAuthenticated() && !auth.getName().equals("anonymousUser");

        // 세션에서 관리자 로그인 정보 확인
        HttpSession session = request.getSession(false);
        boolean isAdminSession = session != null && session.getAttribute("adminLogin") != null;

        if (isAuthenticated && auth != null) {
            // 로그인된 사용자인 경우
            boolean isAdmin = auth.getAuthorities().stream()
                    .anyMatch(authority ->
                        authority.getAuthority().equals("ROLE_ADMIN") ||
                        authority.getAuthority().equals("ROLE_MANAGER"));

            if (isAdmin || isAdminSession) {
                // 관리자인 경우 관리자 대시보드로 리다이렉트
                logger.debug("Admin user accessing home, redirecting to /manage");
                return "redirect:/manage";
            } else {
                // 일반 사용자인 경우
                logger.debug("Regular user accessing home: {}", auth.getName());
                model.addAttribute("pageTitle", "한림공원 QR 체험");
                model.addAttribute("pageDescription", "한림공원에서 QR 코드를 스캔하여 다양한 퀴즈와 게임을 즐겨보세요.");
                model.addAttribute("isAuthenticated", true);
                model.addAttribute("username", auth.getName());
                return "index";
            }
        } else if (isAdminSession) {
            // 관리자 세션이 있는 경우 관리자 대시보드로 리다이렉트
            logger.debug("Admin session found, redirecting to /manage");
            return "redirect:/manage";
        } else {
            // 비로그인 사용자인 경우 로그인 페이지로 리다이렉트
            logger.debug("Anonymous user accessing home, redirecting to login");
            return "redirect:/user/login";
        }
    }

}
